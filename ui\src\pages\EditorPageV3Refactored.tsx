/**
 * Production-ready EditorPageV3 - Refactored with modular architecture
 * Clean, maintainable, and scalable implementation
 */

import React, { useEffect, useState, useRef } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useEditorV3, ChatMessage } from '../hooks/useEditorV3';
import SeamlessProgressiveRenderer from '../components/ProgressiveRenderer';
import { SPAShell } from '../components/SPAShell';
import ErrorBoundary from '../components/Editor/ErrorBoundary';
import ModalDialogs from '../components/Editor/ModalDialogs';
import ImplementationModal from '../components/Editor/ImplementationModal';
// VERSIONING DISABLED: Commented out versioning-related imports
// import PrototypeVersionSwitcher from '../components/Editor/PrototypeVersionSwitcher';
import { previewEnhancement, PromptEnhancement } from '../services/promptEnhancementService';
import { createElementSelectorManager, ElementSelection, extractEditParameters } from '../services/elementSelectorService';
import EditModeControls from '../components/Editor/EditModeControls';
import { getSession, getPageList, getProjectList, updatePage, deletePage } from '../services/pageGenService';
// import { usePrototypeVersions } from '../hooks/usePrototypeVersions';
// import { usePrototypeVersioning } from '../hooks/usePrototypeVersioning';
//import VersioningDebugPanel from '../components/Debug/VersioningDebugPanel';
import { createPageWithCleanName, generatePageContentPrompt } from '../services/pageCreationService';
import type { PendingPageCreation } from '../types/pageCreation';
import { Bars3Icon } from '@heroicons/react/24/outline';
import { PromptInput, DeviceType } from '../components/shared/PromptInput';
import { PageSidebar, Page as SidebarPage } from '../components/shared/PageSidebar';
import { PlanDisplay } from '../components/PlanDisplay';

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// ============================================================================
// TYPES
// ============================================================================

interface LocationState {
  prompt?: string;
  plan?: any;
  projectId?: number;
  sessionId?: string;
  initialGeneration?: boolean;
  loadExistingPage?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EditorPageV3Refactored() {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();

  // Get projectId from URL params or location state
  const urlProjectId = params.projectId ? parseInt(params.projectId, 10) : undefined;
  const { prompt, plan, projectId: stateProjectId, sessionId, initialGeneration, loadExistingPage } = (location.state as LocationState) || {};
  const projectId = urlProjectId || stateProjectId;

  // Use the custom hook for all editor functionality
  const { state, actions } = useEditorV3({ projectId, sessionId });

  // VERSIONING DISABLED: Commented out version switching state
  // const [isVersionSwitching, setIsVersionSwitching] = useState(false);

  // VERSIONING DISABLED: Commented out first version tracking
  // const [hasFirstVersion, setHasFirstVersion] = useState(false);

  // VERSIONING DISABLED: Commented out prototype version management
  /*
  const {
    versionLabels,
    currentVersionLabel,
    isLoading: isVersionLoading,
    error: versionError,
    switchToVersion,
    clearError: clearVersionError,
    refreshVersions
  } = usePrototypeVersions({
    prototypeId: projectId || null,
    onVersionChange: (version) => {
      console.log('🔄 Prototype version changed:', version);

      // Mark as version switching to prevent new version creation
      setIsVersionSwitching(true);

      // Update editor content with the selected version
      actions.setHtmlContent(version.html);
      actions.setStableIframeContent(version.html);

      // Clear version switching flag after content is set
      setTimeout(() => {
        setIsVersionSwitching(false);
        console.log('✅ Version switching completed');
      }, 1500);
    }
  });
  */

  // VERSIONING DISABLED: Commented out automatic prototype versioning
  /*
  const {
    isVersionCreationInProgress,
    cancelPendingVersionCreation
  } = usePrototypeVersioning({
    prototypeId: projectId || null,
    htmlContent: state.htmlContent,
    isGenerating: state.isGenerating,
    currentPageId: state.currentPageId,
    pages: state.pages,
    isVersionSwitching: isVersionSwitching,
    onVersionCreated: (versionId) => {
      console.log('🎉 New prototype version created:', versionId);
      // Mark that we now have versions and refresh the list
      setHasFirstVersion(true);
      refreshVersions();
    }
  });
  */

  // Debug streaming content changes
  useEffect(() => {
    console.log('🎬 EditorPageV3Refactored: Streaming content changed:', {
      isGenerating: state.isGenerating,
      hasStreamingContent: !!state.streamingContent,
      streamingContentLength: state.streamingContent?.length || 0,
      streamingContentPreview: state.streamingContent?.substring(0, 100) + '...',
      hasHtmlContent: !!state.htmlContent,
      htmlContentLength: state.htmlContent?.length || 0
    });
  }, [state.streamingContent, state.isGenerating, state.htmlContent]);

  // VERSIONING DISABLED: Commented out first version creation listener
  /*
  useEffect(() => {
    const handleFirstVersionCreated = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId } = event.detail;
      if (eventPrototypeId === projectId) {
        console.log('🎉 First version created, enabling version controls (readdy.ai style)');
        setHasFirstVersion(true);
        // Trigger version loading
        refreshVersions();
      }
    };

    window.addEventListener('firstVersionCreated', handleFirstVersionCreated as EventListener);

    return () => {
      window.removeEventListener('firstVersionCreated', handleFirstVersionCreated as EventListener);
    };
  }, [projectId, refreshVersions]);
  */

  // Debug state for streaming content
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // Test progressive rendering
  const [isTestingProgressive, setIsTestingProgressive] = useState(false);

  const testProgressiveRendering = async () => {
    console.log('🎬 Testing progressive rendering...');
    setIsTestingProgressive(true);
    actions.setStreamingContent(''); // Clear any existing content

    const testHtml = `
<div class="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
  <h1 class="text-2xl font-bold text-gray-900 mb-4">Progressive Rendering Test</h1>
  <p class="text-gray-600 mb-4">This content is being rendered progressively, element by element.</p>
  <div class="grid grid-cols-2 gap-4 mb-4">
    <div class="bg-blue-100 p-4 rounded">
      <h3 class="font-semibold text-blue-900">Feature 1</h3>
      <p class="text-blue-700">Smooth animations</p>
    </div>
    <div class="bg-green-100 p-4 rounded">
      <h3 class="font-semibold text-green-900">Feature 2</h3>
      <p class="text-green-700">Progressive loading</p>
    </div>
  </div>
  <div class="bg-yellow-100 p-4 rounded mb-4">
    <h3 class="font-semibold text-yellow-900">Feature 3</h3>
    <p class="text-yellow-700">No jitter or flashing</p>
  </div>
  <button class="bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700">
    Test Button
  </button>
</div>
    `.trim();

    console.log('🧪 Starting progressive rendering test with streaming content');

    // Simulate streaming by progressively adding content
    const lines = testHtml.split('\n');
    let accumulatedContent = '';

    for (let i = 0; i < lines.length; i++) {
      accumulatedContent += lines[i] + '\n';
      console.log('🧪 Setting streaming content:', {
        lineIndex: i,
        totalLines: lines.length,
        accumulatedLength: accumulatedContent.length,
        currentLine: lines[i].trim()
      });
      actions.setStreamingContent(accumulatedContent);
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate streaming delay
    }

    console.log('🧪 Progressive rendering test completed');

    setTimeout(() => {
      setIsTestingProgressive(false);
      actions.setStreamingContent(''); // Clear after test
    }, 3000);
  };

  // Track if initial generation has been triggered to prevent duplicates
  const [hasTriggeredInitialGeneration, setHasTriggeredInitialGeneration] = useState(false);

  // Track page loading state
  const [isLoadingPage, setIsLoadingPage] = useState(false);

  // Track generation progress for better user feedback
  const [generationProgress, setGenerationProgress] = useState<{
    isActive: boolean;
    startTime: number;
    message: string;
  } | null>(null);

  // View mode for generation preview
  const [generationViewMode, setGenerationViewMode] = useState<'preview' | 'code'>('preview');

  // View mode for content display
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');

  // Project pages state
  const [projectPages, setProjectPages] = useState<any[]>([]);
  const [isLoadingProjectPages, setIsLoadingProjectPages] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);

  // New page creation state
  const [isCreatingNewPage, setIsCreatingNewPage] = useState(false);
  const [newPagePrompt, setNewPagePrompt] = useState('');
  const [showPlanReview, setShowPlanReview] = useState(false);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [generatedPageName, setGeneratedPageName] = useState<string>('');

  // Page management state
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [renamingPageId, setRenamingPageId] = useState<string | null>(null);
  const [newPageName, setNewPageName] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Left pane view mode
  const [isLeftPaneCollapsed, setIsLeftPaneCollapsed] = useState(false);

  // Function to refresh project pages
  const refreshProjectPages = async () => {
    if (projectId) {
      setIsLoadingProjectPages(true);
      try {
        const response = await getPageList(projectId);
        if (response.sessions) {
          setProjectPages(response.sessions);
        }
      } catch (error) {
        console.error('❌ Error refreshing project pages:', error);
      } finally {
        setIsLoadingProjectPages(false);
      }
    }
  };

  // Handle page rename for shared PageSidebar component
  const handlePageRenameShared = async (pageId: number, newName: string) => {
    try {
      // Call API to rename page
      const response = await updatePage(pageId, newName);

      if (response.success) {
        // Update local state
        setProjectPages(prev => prev.map(page =>
          page.id === pageId ? { ...page, title: newName } : page
        ));

        console.log(`✅ Page renamed to "${newName}"`);
      } else {
        throw new Error('Failed to rename page');
      }
    } catch (error) {
      console.error('Error renaming page:', error);
      // You could add a toast notification here
      throw error; // Re-throw to let the component handle the error state
    }
  };

  // Handle page delete for shared PageSidebar component
  const handlePageDeleteShared = async (pageId: number) => {
    try {
      // Call API to delete page
      const response = await deletePage(pageId);

      if (response.success) {
        // If we're deleting the current page, clear the editor
        if (currentSessionId === pageId.toString()) {
          setCurrentSessionId(null);
          actions.setHtmlContent('');
          actions.setStableIframeContent('');
        }

        // Update local state - remove the deleted page
        setProjectPages(prev => prev.filter(page => page.id !== pageId));

        // Check if this was the last page - if so, show create new page UI
        const updatedPages = projectPages.filter(p => p.id !== pageId);
        if (updatedPages.length === 0) {
          console.log('📄 Last page deleted, switching to create new page mode');
          setIsCreatingNewPage(true);
          setShowPlanReview(false);
          setNewPagePrompt('');
          setGeneratedPageName('');
        }

        console.log('✅ Page deleted successfully');
      } else {
        throw new Error('Failed to delete page');
      }
    } catch (error) {
      console.error('Error deleting page:', error);
      // You could add a toast notification here
      throw error; // Re-throw to let the component handle the error state
    }
  };

  // Element selector state for ChatInterface
  const [elementSelectorActive, setElementSelectorActive] = useState(false);

  // Prompt enhancement state
  const [enhancement, setEnhancement] = useState<PromptEnhancement | null>(null);
  const [showEnhancement, setShowEnhancement] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [originalMessage, setOriginalMessage] = useState<string>(''); // Store original since input is cleared

  // Element selector state (modular)
  const [isEditModeActive, setIsEditModeActive] = useState(false);
  const [selectedElement, setSelectedElement] = useState<ElementSelection | null>(null);
  const spaShellRef = useRef<HTMLDivElement>(null);
  const [elementSelectorManager] = useState(() =>
    createElementSelectorManager(
      (selection) => setSelectedElement(selection),
      (isActive) => setIsEditModeActive(isActive),
      undefined, // no iframe ref for SPAShell mode
      spaShellRef, // container ref for direct DOM access
      'direct' // use direct DOM mode
    )
  );

  // Resizable panel dimensions
  const [pagesPanelWidth, setPagesPanelWidth] = useState(280);
  const [chatPanelWidth, setChatPanelWidth] = useState(280); // Reduced chat panel for more preview space
  const [pagesPanelCollapsed, setPagesPanelCollapsed] = useState(true);

  // Resize state
  const [isResizing, setIsResizing] = useState<'pages' | 'chat' | null>(null);

  // Resize handlers
  const handleMouseDown = (type: 'pages' | 'chat') => (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(type);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;

    if (isResizing === 'pages') {
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      setPagesPanelWidth(newWidth);
    } else if (isResizing === 'chat') {
      const newWidth = Math.max(300, Math.min(600, window.innerWidth - e.clientX));
      setChatPanelWidth(newWidth);
    }
  };

  const handleMouseUp = () => {
    setIsResizing(null);
  };

  // Add global mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing]);

  // Additional state for robust UX
  const [showPageCreationDialog, setShowPageCreationDialog] = useState(false);
  const [pendingPageCreation, setPendingPageCreation] = useState<PendingPageCreation | null>(null);
  const [showLinkingDialog, setShowLinkingDialog] = useState(false);
  const [linkingProgress, setLinkingProgress] = useState<{
    current: number;
    total: number;
    currentPage: string;
  } | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);

  // SPAShell integration state
  const [useSPAMode, setUseSPAMode] = useState(true); // SPA mode always enabled (UI controls hidden)
  const [spaEditMode, setSpaEditMode] = useState(false); // SPAShell edit mode

  // Project selection state
  const [showProjectSelector, setShowProjectSelector] = useState(!projectId);
  const [availableProjects, setAvailableProjects] = useState<any[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);

  // ============================================================================
  // INITIALIZATION EFFECTS
  // ============================================================================

  // Track if we've already shown the linking suggestion
  const [hasShownLinkingSuggestion, setHasShownLinkingSuggestion] = useState(false);

  // Simplified check for 2+ pages that need linking
  const checkIfPagesNeedLinking = (pages: any[]) => {
    const pagesWithContent = pages.filter(p => p.content && p.content.length > 50);
    // For simplicity: if we have 2+ pages, assume they need linking
    return pagesWithContent.length >= 2;
  };

  // Watch for page updates and auto-link when we have 2+ pages (with proper guards)
  useEffect(() => {
    const needsLinking = checkIfPagesNeedLinking(state.pages);

    // DISABLED: Auto-linking causes multiple popups and blinking
    // User can manually link pages using the Link Pages button
    if (false && needsLinking && !hasShownLinkingSuggestion && !showLinkingDialog && !linkingProgress) {
      console.log('🔗 Auto-linking disabled to prevent multiple popups');
      setHasShownLinkingSuggestion(true);
      handleLinkPages(); // Link immediately without dialog
    }
  }, [state.pages, hasShownLinkingSuggestion, showLinkingDialog, linkingProgress]);

  // Handle initial generation from plan page
  useEffect(() => {
    console.log('🔍 Initial generation check:', {
      initialGeneration,
      hasPrompt: !!prompt,
      hasHtmlContent: !!state.htmlContent,
      hasTriggeredInitialGeneration,
      shouldTrigger: false
    });

    if (initialGeneration && prompt && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('🚀 Starting initial generation from plan page (ONCE)');
      console.log('📋 Plan data structure:', plan ? Object.keys(plan) : 'No plan');
      console.log('💬 Original prompt:', prompt);
      console.log('🔍 Plan sections:', plan?.sections?.length || 0);
      console.log('🔍 Plan features:', plan?.features?.length || 0);

      // Mark as triggered to prevent duplicates
      setHasTriggeredInitialGeneration(true);

      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      actions.addMessage(userMessage);

      // Add plan message if we have a plan
      if (plan) {
        let planData;
        try {
          // Parse plan if it's a string
          planData = typeof plan === 'string' ? JSON.parse(plan) : plan;
        } catch (e) {
          planData = plan;
        }

        const planContent = formatPlanForDisplay(planData);
        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        actions.addMessage(planMessage);
      }

      // Create comprehensive prompt using BOTH original prompt AND plan data
      let comprehensivePrompt = prompt;

      if (plan && typeof plan === 'object') {
        // Build detailed prompt from plan data
        comprehensivePrompt = `${prompt}

📋 **DETAILED IMPLEMENTATION PLAN:**

🎯 **Project Overview:**
${plan.overview || 'Create a professional, modern design'}

🏗️ **Implementation Requirements:**`;

        // Add each section from the plan
        if (plan.sections && Array.isArray(plan.sections)) {
          plan.sections.forEach((section: any, index: number) => {
            comprehensivePrompt += `

${index + 1}. **${section.title}**
   ${section.description}`;

            if (section.details && Array.isArray(section.details)) {
              section.details.forEach((detail: string) => {
                comprehensivePrompt += `
   • ${detail}`;
              });
            }
          });
        }

        // Add features
        if (plan.features && Array.isArray(plan.features)) {
          comprehensivePrompt += `

✨ **Key Features to Implement:**`;
          plan.features.forEach((feature: string) => {
            comprehensivePrompt += `
• ${feature}`;
          });
        }

        // Add accessibility requirements
        if (plan.accessibility && Array.isArray(plan.accessibility)) {
          comprehensivePrompt += `

♿ **Accessibility Requirements:**`;
          plan.accessibility.forEach((item: string) => {
            comprehensivePrompt += `
• ${item}`;
          });
        }
      }

      // Add modal-ready instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🎯 Using comprehensive prompt with plan data:', comprehensivePrompt.substring(0, 200) + '...');
      console.log('📏 Final prompt length:', comprehensivePrompt.length);
      console.log('🔍 Plan sections included:', plan?.sections?.length || 0);
      console.log('🔍 Plan features included:', plan?.features?.length || 0);

      // Start generation with comprehensive prompt
      actions.generateFromPrompt(comprehensivePrompt);
    }
  }, [initialGeneration, prompt, state.htmlContent, plan, hasTriggeredInitialGeneration]);

  // Listen for navigation messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NAVIGATE_TO_PAGE') {
        const { pageId, pageName } = event.data;
        console.log('🔗 Navigation message received:', { pageId, pageName });

        // Debug: Check if this is a known page name
        const existingPageNames = state.pages.map(p => p.name);
        console.log('🔗 Existing pages:', existingPageNames);
        console.log('🔗 Clicked page name:', pageName);

        // Handle navigation click
        handleNavigationClick({
          textContent: pageName,
          isNavigation: true
        });
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [state.pages]);

  // Load existing page content when sessionId is provided
  useEffect(() => {
    if (loadExistingPage && sessionId && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('📄 Loading existing page content for session:', sessionId);

      const loadPageContent = async () => {
        setIsLoadingPage(true);

        try {
          const response = await getSession(sessionId);
          if (response.success && response.session) {
            console.log('✅ Page content loaded successfully');
            console.log('🔧 Page HTML length:', response.session.page_html?.length);
            console.log('🔧 Page HTML starts with:', response.session.page_html?.substring(0, 100));
            console.log('🔧 Page HTML includes userDashboard:', response.session.page_html?.includes('userDashboard'));

            // Set the HTML content in the editor
            actions.setHtmlContent(response.session.page_html);
            actions.setStableIframeContent(response.session.page_html);

            console.log('🔧 Content set in state, checking state values...');
            console.log('🔧 State htmlContent length:', state.htmlContent?.length);
            console.log('🔧 State stableIframeContent length:', state.stableIframeContent?.length);

            // Add a welcome message
            actions.addMessage({
              role: 'assistant',
              content: `📄 Loaded existing page. You can now edit this page or ask me to make changes.`,
              timestamp: new Date()
            });

            // Mark as triggered to prevent other loading attempts
            setHasTriggeredInitialGeneration(true);

            // Update current session ID for UI highlighting
            setCurrentSessionId(sessionId);
          }
        } catch (error) {
          console.error('❌ Error loading page content:', error);
          actions.addMessage({
            role: 'assistant',
            content: `❌ Failed to load page content. Please try again or create a new page.`,
            timestamp: new Date()
          });
        } finally {
          setIsLoadingPage(false);
        }
      };

      loadPageContent();
    }
  }, [loadExistingPage, sessionId, state.htmlContent, hasTriggeredInitialGeneration, actions]);

  // Load available projects when no project is specified
  useEffect(() => {
    if (!projectId) {
      console.log('📄 No project specified, loading available projects');

      const loadProjects = async () => {
        setIsLoadingProjects(true);
        try {
          const response = await getProjectList(1, 50); // Load first 50 projects
          if (response.projects) {
            console.log('✅ Available projects loaded:', response.projects.length);
            setAvailableProjects(response.projects);
          }
        } catch (error) {
          console.error('❌ Error loading projects:', error);
        } finally {
          setIsLoadingProjects(false);
        }
      };

      loadProjects();
    }
  }, [projectId]);

  // Load project pages when component mounts or projectId changes
  useEffect(() => {
    console.log('🔍 Project pages loading check:', {
      projectId,
      hasHtmlContent: !!state.htmlContent,
      hasStableContent: !!state.stableIframeContent,
      hasTriggeredInitialGeneration,
      shouldLoadPages: !!projectId,
      hasLocationState: !!(location.state),
      loadExistingPage,
      sessionId
    });

    if (projectId) {
      console.log('📄 Loading project pages for project:', projectId);

      const loadProjectPages = async () => {
        setIsLoadingProjectPages(true);
        try {
          const response = await getPageList(projectId);
          if (response.sessions) {
            console.log('✅ Project pages loaded:', response.sessions.length);
            setProjectPages(response.sessions);

            // Auto-expand pages panel when project pages are loaded
            if (response.sessions.length > 0) {
              setPagesPanelCollapsed(false);

              // Auto-load first page if no page is currently selected and no generation has been triggered
              if (!currentSessionId && !state.htmlContent && !state.stableIframeContent && !hasTriggeredInitialGeneration) {
                console.log('📄 Auto-loading first page for editing');
                const firstPage = response.sessions[0];

                // Mark as triggered to prevent other loading attempts
                setHasTriggeredInitialGeneration(true);

                handleProjectPageSelect(firstPage);
              } else {
                console.log('📄 Pages available, current page already selected or generation already triggered');
              }
            } else {
              // If no pages exist, just show empty state - don't auto-create
              console.log('📄 No pages found, showing empty state');
              // Don't automatically show create new page UI
              // User can manually click to create a new page
            }
          }
        } catch (error) {
          console.error('❌ Error loading project pages:', error);
        } finally {
          setIsLoadingProjectPages(false);
        }
      };

      loadProjectPages();
    }
  }, [projectId]);

  // Listen for auto-save completion events and refresh page list
  useEffect(() => {
    const handlePageSaved = async (event: CustomEvent) => {
      const saveData = event.detail;
      console.log('🔄 Page saved event received:', saveData);

      // Immediately refresh page list to ensure new page appears
      console.log('🔄 Immediately refreshing page list after save');
      await refreshProjectPages();

      // If we have session ID, switch to the newly created page
      if (saveData.sessionId) {
        setCurrentSessionId(saveData.sessionId.toString());

        // Load the new page content
        try {
          const response = await getSession(saveData.sessionId.toString());
          if (response.success && response.session) {
            // Set the content in the editor
            actions.setHtmlContent(response.session.page_html);
            actions.setStableIframeContent(response.session.page_html);

            // Check if page exists in state, if not add it, then switch to it
            const existingPageInState = state.pages.find(p => p.id === saveData.sessionId.toString());
            if (existingPageInState) {
              // Page exists, just switch to it
              actions.switchToPage(saveData.sessionId.toString());
            } else {
              // Page doesn't exist in state, add it
              const pageForState = {
                id: saveData.sessionId.toString(),
                name: saveData.pageTitle || 'New Page',
                content: response.session.page_html,
                isActive: false // No active flags needed, just use currentSessionId for UI
              };
              actions.addPage(pageForState);
            }

            // Add success message
            actions.addMessage({
              role: 'assistant',
              content: `✅ Page "${saveData.pageTitle || 'New Page'}" has been automatically saved and is now available in the page list!`,
              timestamp: new Date()
            });

            // Schedule another refresh after a short delay to ensure consistency
            setTimeout(async () => {
              console.log('🔄 Secondary page list refresh for consistency');
              await refreshProjectPages();
            }, 1000);
          }
        } catch (error) {
          console.error('❌ Error loading newly saved page:', error);
          // Still refresh the page list even if loading fails
          setTimeout(async () => {
            console.log('🔄 Fallback page list refresh after error');
            await refreshProjectPages();
          }, 2000);
        }
      }
    };

    const handlePageSaveError = (event: CustomEvent) => {
      const errorData = event.detail;
      console.error('❌ Page save error event received:', errorData);

      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to auto-save page: ${errorData.error}. You may need to save manually.`,
        timestamp: new Date()
      });
    };

    const handlePageSaveSkipped = (event: CustomEvent) => {
      const skipData = event.detail;
      console.warn('⚠️ Page save skipped event received:', skipData);

      actions.addMessage({
        role: 'assistant',
        content: `⚠️ Page auto-save was skipped: ${skipData.reason}. You may need to save manually.`,
        timestamp: new Date()
      });
    };

    const handleGenerationCompleted = async (event: CustomEvent) => {
      const { pageTitle, generatedHTML, fallback } = event.detail;
      console.log('🔄 Generation completed event received (fallback mode):', { pageTitle, fallback, hasHTML: !!generatedHTML });

      if (fallback) {
        // This is a fallback scenario - generation completed but no auto-save event received
        // Refresh the page list to check if the page was actually created
        await refreshProjectPages();

        // Add a delay and check again to give the backend time to save
        setTimeout(async () => {
          console.log('🔄 Performing delayed page list refresh for fallback scenario');
          await refreshProjectPages();

          // Look for a recently created page that matches the title
          const recentPages = projectPages.filter(page => {
            const pageCreatedTime = new Date(page.created_at).getTime();
            const timeDiff = Date.now() - pageCreatedTime;
            return timeDiff < 60000; // Created within the last minute
          });

          if (recentPages.length > 0) {
            const latestPage = recentPages[recentPages.length - 1];
            console.log('🔄 Found recently created page in fallback mode:', latestPage);

            // Switch to the newly found page
            setCurrentSessionId(latestPage.id);

            try {
              const response = await getSession(latestPage.id);
              if (response.success && response.session) {
                actions.setHtmlContent(response.session.page_html);
                actions.setStableIframeContent(response.session.page_html);

                actions.addMessage({
                  role: 'assistant',
                  content: `✅ Found your page "${latestPage.title || pageTitle}"! It was successfully created and is now available.`,
                  timestamp: new Date()
                });
              }
            } catch (error) {
              console.error('❌ Error loading fallback page:', error);
            }
          } else {
            actions.addMessage({
              role: 'assistant',
              content: `⚠️ Page generation completed but the page was not found in the database. Please try creating the page again.`,
              timestamp: new Date()
            });
          }
        }, 5000); // 5 second delay for fallback check
      }
    };

    // Add event listeners
    window.addEventListener('pageSaved', handlePageSaved as EventListener);
    window.addEventListener('pageSaveError', handlePageSaveError as EventListener);
    window.addEventListener('pageSaveSkipped', handlePageSaveSkipped as EventListener);
    window.addEventListener('generationCompleted', handleGenerationCompleted as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pageSaved', handlePageSaved as EventListener);
      window.removeEventListener('pageSaveError', handlePageSaveError as EventListener);
      window.removeEventListener('pageSaveSkipped', handlePageSaveSkipped as EventListener);
      window.removeEventListener('generationCompleted', handleGenerationCompleted as EventListener);
    };
  }, [projectId, refreshProjectPages, actions, state.pages, projectPages]);

  // Track generation progress and provide user feedback
  // Only show progress for actual content generation, not for loading existing pages
  useEffect(() => {
    if (state.isGenerating && !generationProgress && !isLoadingPage) {
      // Start tracking generation progress only for actual generation, not page loading
      console.log('🔄 Starting generation progress tracking');
      setGenerationProgress({
        isActive: true,
        startTime: Date.now(),
        message: 'Starting page generation...'
      });
    } else if (!state.isGenerating && generationProgress) {
      // Generation completed
      console.log('🔄 Generation completed, clearing progress indicator');
      setGenerationProgress(null);
    }
  }, [state.isGenerating, generationProgress, isLoadingPage]);

  // Safety check: Reset generation progress if it's been active too long without state.isGenerating
  useEffect(() => {
    if (generationProgress?.isActive && !state.isGenerating) {
      const elapsed = Date.now() - generationProgress.startTime;
      // If progress has been active for more than 5 seconds without state.isGenerating, reset it
      if (elapsed > 5000) {
        console.log('🔄 Resetting stuck generation progress indicator');
        setGenerationProgress(null);
      }
    }
  }, [generationProgress, state.isGenerating]);

  // Reset generation progress when loading pages (not generating)
  useEffect(() => {
    if (isLoadingPage && generationProgress) {
      console.log('🔄 Page loading detected, clearing generation progress');
      setGenerationProgress(null);
    }
  }, [isLoadingPage]); // Remove generationProgress from dependency array to prevent infinite loop

  // Cleanup generation progress on component unmount
  useEffect(() => {
    return () => {
      if (generationProgress) {
        console.log('🔄 Component unmounting, clearing generation progress');
        setGenerationProgress(null);
      }
    };
  }, []); // Empty dependency array - cleanup should only run on unmount

  // Update progress messages based on elapsed time
  useEffect(() => {
    if (!generationProgress?.isActive) return;

    // Store the initial startTime to avoid dependency changes
    const initialStartTime = generationProgress.startTime;

    const updateProgressMessage = () => {
      const elapsed = Date.now() - initialStartTime;
      let message = 'Generating your page...';

      if (elapsed > 30000) { // 30 seconds
        message = 'Generation is taking longer than usual, please wait...';
      }
      if (elapsed > 60000) { // 1 minute
        message = 'Still working on your page, this may take a few more moments...';
      }
      if (elapsed > 120000) { // 2 minutes
        message = 'Complex generation in progress, almost done...';
      }
      if (elapsed > 180000) { // 3 minutes
        message = 'Generation is taking longer than expected, but still processing...';
      }

      setGenerationProgress(prev => prev ? { ...prev, message } : null);
    };

    // Update message immediately and then every 10 seconds
    updateProgressMessage();
    const interval = setInterval(updateProgressMessage, 10000);

    return () => clearInterval(interval);
  }, [generationProgress?.isActive]); // Fixed: Only depend on isActive, not startTime



  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  // Handle project selection
  const handleProjectSelect = (selectedProjectId: number) => {
    console.log('📄 Project selected:', selectedProjectId);
    // Navigate to the same route with the project ID
    navigate(`/editor-v3-refactored/${selectedProjectId}`);
  };

  // Enhancement handlers
  const handleUseEnhanced = async () => {
    if (enhancement) {
      console.log('🎯 User chose enhanced prompt');
      setShowEnhancement(false);
      setEnhancement(null);

      // Proceed with enhanced prompt
      await proceedWithSubmit(enhancement.enhancedPrompt);
      setOriginalMessage(''); // Clear after use
    }
  };

  const handleCloseEnhancement = async () => {
    console.log('🔄 User chose original prompt');
    setShowEnhancement(false);
    setEnhancement(null);

    // Proceed with original prompt (stored since input was cleared)
    if (originalMessage.trim()) {
      await proceedWithSubmit(originalMessage.trim());
      setOriginalMessage(''); // Clear after use
    }
  };

  const handleChatSubmit = async (message: string) => {
    console.log('🔥 handleChatSubmit called with message:', message);

    // Clear input immediately (normal chat behavior)
    actions.clearInput();

    // Try enhancement first (only for edits)
    const enhancementShown = await triggerEnhancement(message);
    if (enhancementShown) {
      console.log('🔧 Enhancement shown, waiting for user choice');
      return; // Don't proceed yet, wait for user to choose enhanced/original
    }

    // Proceed with normal flow
    await proceedWithSubmit(message);
  };

  const proceedWithSubmit = async (message: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    actions.addMessage(userMessage);
    // Input already cleared in handleChatSubmit

    // Determine if this is an edit or new generation
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    console.log('🔥 Content check:', {
      hasHtmlContent: state.htmlContent.length > 0,
      hasStableContent: state.stableIframeContent.length > 0,
      hasContent,
      willEdit: hasContent,
      willGenerate: !hasContent
    });

    if (hasContent) {
      console.log('🔥 Calling editContent for existing content');

      // Extract element selector parameters for targeted editing
      const editParams = extractEditParameters(selectedElement);

      await actions.editContent(
        message,
        [], // no additional messages
        editParams.elementSelector,
        undefined, // implementationType - not needed for chat edits
        selectedElement // pass the full selectedElement data
      );

      // After successful edit, reload the page content from database to ensure consistency
      if (currentSessionId) {
        console.log('🔄 Reloading page content after successful edit...');
        const reloadSuccess = await actions.reloadCurrentPage(currentSessionId);
        if (reloadSuccess) {
          console.log('✅ Page content reloaded successfully after edit');
        } else {
          console.warn('⚠️ Failed to reload page content after edit');
        }
      }
    } else {
      console.log('🔥 Calling generateFromPrompt for new content');
      await actions.generateFromPrompt(message);
    }
  };

  // Step 1: Generate Intent like Readdy.ai does
  const generateIntentFromElement = async (element: any) => {
    // DISABLED: Using new IntentBasedEditor component instead
    console.log('🔥 Old intent generation disabled - using new IntentBasedEditor system');
    return;

    // OLD CODE DISABLED TO PREVENT DUPLICATE CALLS
    // Prevent multiple intent calls for the same element
    if (element.intentGenerating || element.intentData || isGeneratingIntent) {
      console.log('🔥 Intent already generating or exists for this element');
      return;
    }

    // Mark this element as being processed
    element.intentGenerating = true;
    setIsGeneratingIntent(true);

    console.log('🔥 Starting intent generation for element:', element.textContent);
    const tagName = element.tagName?.toLowerCase() || 'div';
    const elementCode = element.outerHTML || `<${tagName}>${element.textContent || ''}</${tagName}>`;

    try {
      // Step 1: Call intent generation API (like Readdy's /api/page_gen/generate_intent)
      const response = await fetch('/api/llm/v3/generate-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          elementCode,
          htmlContent: state.htmlContent || state.stableIframeContent,
          conversationHistory: state.messages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 'OK' && result.data) {
          // Display the intent and suggestion like Readdy does
          actions.addMessage({
            role: 'assistant',
            content: result.data.userIntent,
            timestamp: new Date()
          });

          if (result.data.suggestion) {
            actions.addMessage({
              role: 'assistant',
              content: result.data.suggestion,
              timestamp: new Date()
            });
          }

          // Store the intent for later use in implementation
          element.intentData = result.data;
        }
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      // Fallback message
      actions.addMessage({
        role: 'assistant',
        content: `I understand you want to implement functionality for "${element.textContent}". Let me help you with that.`,
        timestamp: new Date()
      });
    } finally {
      element.intentGenerating = false;
      setIsGeneratingIntent(false);
    }
  };

  // Enhanced element click handler that works with both PreviewPanel and SPAShell
  const handleElementClick = async (element: any) => {
    console.log('🔥 Enhanced element click handler:', element);

    // Handle navigation clicks
    if (element.isNavigation) {
      console.log('🔥 Navigation detected, handling navigation click');
      handleNavigationClick(element);
      return;
    }

    // Handle interactive elements that need implementation
    if ((element.implementationType && element.implementationReason) || element.isInteractive) {
      console.log('🔥 Interactive element needs implementation:', element.implementationReason);

      // Convert element data to proper ElementInfo format
      const elementInfo = {
        selector: element.selector || '',
        tagName: element.tagName || '',
        textContent: element.textContent || '',
        attributes: element.attributes || {},
        isNavigation: element.isNavigation || false,
        isInteractive: element.isInteractive || true,
        implementationType: element.implementationType,
        implementationReason: element.implementationReason,
        outerHTML: element.outerHTML, // Ensure outerHTML is preserved
        intentData: element.intentData
      };

      console.log('🔍 Converted element info:', {
        hasOuterHTML: !!elementInfo.outerHTML,
        outerHTMLLength: elementInfo.outerHTML?.length || 0,
        outerHTMLPreview: elementInfo.outerHTML?.substring(0, 100) + '...'
      });

      // Set selected element and show implementation modal
      actions.setSelectedElement(elementInfo);
      actions.setShowImplementModal(true);
      return;
    }

    console.log('🔥 Element does not need implementation, ignoring');
  };

  const handleNavigationClick = async (element: any) => {
    const rawPageName = element.textContent.trim();

    try {
      // Create page with clean name using the new service
      const result = await createPageWithCleanName(
        { prompt: rawPageName },
        'navigation-link'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to create page');
      }

      const { pageName, pageId } = result;
      console.log('🔥 Processing navigation click:', { rawPageName, pageName, pageId });

      // Smart page matching: handle both "Reports" and "Reports Page" scenarios
      const existingPage = state.pages.find(p => {
        const normalizedPageName = pageName!.toLowerCase().trim();
        const normalizedExistingName = p.name.toLowerCase().trim();

        // Direct matches
        if (p.id === pageId || normalizedExistingName === normalizedPageName) {
          return true;
        }

        // Handle "Reports" → "Reports Page" mismatch
        if (normalizedExistingName === normalizedPageName + ' page') {
          return true;
        }

        // Handle "Reports Page" → "Reports" mismatch
        if (normalizedPageName === normalizedExistingName + ' page') {
          return true;
        }

        // Handle page ID matches (e.g., "reports" matches "reports-page")
        const clickedPageId = pageId;
        const existingPageId = p.id;
        const clickedPageIdWithPage = `${pageId}-page`;

        if (existingPageId === clickedPageId || existingPageId === clickedPageIdWithPage || clickedPageId === existingPageId) {
          return true;
        }

        return false;
      });

      if (existingPage) {
        console.log('🔥 Page already exists, switching to:', existingPage);
        actions.switchToPage(existingPage.id);

        // Add feedback message
        actions.addMessage({
          role: 'assistant',
          content: `✅ Switched to "${existingPage.name}" page`,
          timestamp: new Date()
        });
      } else {
        console.log('🔥 Page does not exist, showing creation dialog');

        // Show confirmation dialog instead of creating immediately
        const pendingCreation: PendingPageCreation = {
          pageName: pageName!,
          pageId: pageId!,
          source: 'navigation-link',
          originalPrompt: rawPageName
        };
        setPendingPageCreation(pendingCreation);
        setShowPageCreationDialog(true);
      }
    } catch (error) {
      console.error('❌ Error processing navigation click:', error);

      // Fallback to basic page creation
      const pageName = rawPageName;
      const pageId = pageName.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
      const pendingCreation: PendingPageCreation = {
        pageName,
        pageId,
        source: 'navigation-link',
        originalPrompt: rawPageName
      };
      setPendingPageCreation(pendingCreation);
      setShowPageCreationDialog(true);
    }
  };

  const confirmPageCreation = async () => {
    if (!pendingPageCreation) {
      console.log('❌ No pending page creation');
      return;
    }

    const { pageName, pageId, source, originalPrompt } = pendingPageCreation;
    console.log('🚀 Confirming page creation:', { pageName, pageId, source });

    // Close dialog
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    try {
      // Clear current page selection to prepare for new page creation
      setCurrentSessionId(null);
      actions.setHtmlContent('');
      actions.setStableIframeContent('');

      // Add feedback message
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating new "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate content using the same pattern as main page creation
      // This will auto-save the page after HTML generation completes
      const prompt = generatePageContentPrompt(pageName, state.pages);
      console.log('🎯 Generating content with prompt:', prompt.substring(0, 100) + '...');

      try {
        //        await actions.generateFromPrompt(prompt, pageName);
        console.log('✅ Page generation completed');

        // Page will be automatically refreshed and switched when auto-save completes
        // via the pageSaved event listener

        console.log('🔗 Page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Page generation failed:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to generate content for "${pageName}". Please try again.`,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('❌ Error creating page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  const cancelPageCreation = () => {
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    actions.addMessage({
      role: 'assistant',
      content: `❌ Page creation cancelled`,
      timestamp: new Date()
    });
  };

  const handlePageSwitch = (pageId: string) => {
    actions.switchToPage(pageId);

    const page = state.pages.find(p => p.id === pageId);
    if (page) {
      actions.addMessage({
        role: 'assistant',
        content: `📄 Switched to "${page.name}" page`,
        timestamp: new Date()
      });
    }
  };

  // Handle project page selection (load page content directly)
  const handleProjectPageSelect = async (page: any) => {
    console.log('📄 Project page selected:', page);
    console.log('📄 Current state before selection:', {
      currentSessionId,
      stateCurrentPageId: state.currentPageId,
      statePagesCount: state.pages.length
    });

    // Don't reload if it's the same page
    if (currentSessionId === page.id) {
      console.log('📄 Same page selected, ignoring');
      return;
    }

    // Hide new page creation UI when selecting an existing page
    setIsCreatingNewPage(false);
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');
    setGeneratedPlan(null);

    // Set loading state
    setIsLoadingPage(true);

    try {
      // Load the page content
      const response = await getSession(page.id);
      if (response.success && response.session) {
        console.log('✅ Page content loaded successfully');

        // Update current session ID for UI highlighting
        setCurrentSessionId(page.id);

        // Ensure generation state is reset when loading existing pages
        actions.setIsGenerating(false);

        // Update editor state with the page content
        // First, find if this page exists in the state.pages array
        const existingPage = state.pages.find(p => p.id === page.id);
        if (existingPage) {
          // Page exists in state, update its content and switch to it
          actions.updatePage(page.id, { content: response.session.page_html });
          actions.switchToPage(page.id);
        } else {
          // Page doesn't exist in state, add it and switch to it
          const newPage = {
            id: page.id,
            name: page.title || `Page ${page.id}`,
            content: response.session.page_html,
            isActive: false // No active flags needed, just use currentSessionId for UI
          };
          actions.addPage(newPage);
          actions.switchToPage(page.id);
        }

        console.log('🔍 Page selection completed:', {
          pageId: page.id,
          contentLength: response.session.page_html?.length || 0,
          currentPageId: state.currentPageId,
          htmlContentLength: state.htmlContent?.length || 0
        });

        // Add a message to show page switch
        actions.addMessage({
          role: 'assistant',
          content: `📄 Switched to "${page.title || `Page ${page.id}`}". You can now edit this page or ask me to make changes.`,
          timestamp: new Date()
        });

        console.log('✅ Page switched successfully');
        console.log('📄 Current state after selection:', {
          currentSessionId,
          stateCurrentPageId: state.currentPageId,
          statePagesCount: state.pages.length
        });
      }
    } catch (error) {
      console.error('❌ Error loading page content:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to load page content. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setIsLoadingPage(false);
    }
  };

  const handlePageAdd = (page: any) => {
    actions.addPage(page);
    actions.switchToPage(page.id);

    actions.addMessage({
      role: 'assistant',
      content: `📄 Created new page "${page.name}". Describe what content you'd like on this page.`,
      timestamp: new Date()
    });
  };

  // Handle new page creation button click
  const handleCreateNewPage = () => {
    console.log('🆕 Creating new page - resetting all states');

    // Clear current page selection
    setCurrentSessionId(null);

    // Clear editor content
    actions.setHtmlContent('');
    actions.setStableIframeContent('');

    // Reset ALL plan-related states to ensure clean slate
    setShowPlanReview(false);
    setGeneratedPlan(null);
    setGeneratedPageName('');
    setIsGeneratingPlan(false);

    // Show new page creation mode
    setIsCreatingNewPage(true);
    setNewPagePrompt('');

    // Add welcome message
    actions.addMessage({
      role: 'assistant',
      content: `🚀 Ready to create a new page! Describe what kind of page you'd like to create and I'll generate it for you.`,
      timestamp: new Date()
    });
  };

  // Handle new page prompt submission - Generate plan first
  const handleNewPagePromptSubmit = async () => {
    if (!newPagePrompt.trim() || !projectId) return;

    setIsGeneratingPlan(true);

    try {
      // Use the already generated page name
      const pageName = generatedPageName || 'New Page';
      console.log('📄 Using generated page name:', pageName, 'from prompt:', newPagePrompt);

      // Add user message
      actions.addMessage({
        role: 'user',
        content: newPagePrompt,
        timestamp: new Date()
      });

      // Add assistant message about plan generation
      actions.addMessage({
        role: 'assistant',
        content: `🎯 Generating plan for "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate structured plan using the existing API
      const response = await fetch('/api/llm/v3/plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          prompt: newPagePrompt,
          deviceType: 'desktop' // Default to desktop for pages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.plan) {
          console.log('✅ Plan generated successfully:', result.plan);

          // Store the plan and show review
          setGeneratedPlan(result.plan);
          setShowPlanReview(true);

          // Add plan to chat
          actions.addMessage({
            role: 'assistant',
            content: `📋 Here's the plan for your "${pageName}" page. Review it and click "Generate Page" to proceed:

${formatPlanForDisplay(result.plan)}`,
            timestamp: new Date()
          });
        } else {
          throw new Error('No plan returned from API');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Plan generation API failed');
      }

    } catch (error) {
      console.error('❌ Error generating plan:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to generate plan. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Handle plan approval and page generation
  const handleGeneratePageFromPlan = async () => {
    if (!generatedPlan || !newPagePrompt.trim()) {
      console.error('❌ Cannot generate page: missing plan or prompt', {
        hasGeneratedPlan: !!generatedPlan,
        hasNewPagePrompt: !!newPagePrompt.trim(),
        generatedPlan,
        newPagePrompt
      });
      return;
    }

    try {
      const pageName = generatedPageName || 'New Page';

      console.log('🚀 Starting page generation from plan:', {
        pageName,
        generatedPageName,
        newPagePrompt: newPagePrompt.substring(0, 100) + '...',
        projectId,
        hasGeneratedPlan: !!generatedPlan
      });

      // Immediately switch to code/preview mode by resetting plan review state
      setIsCreatingNewPage(false);
      setShowPlanReview(false);

      // Add assistant message about page creation
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating "${pageName}" page based on the approved plan...`,
        timestamp: new Date()
      });

      // Create comprehensive prompt with plan data (like plan generation page)
      let comprehensivePrompt = `${newPagePrompt}

📋 **DETAILED PLAN TO IMPLEMENT:**

**Overview:** ${generatedPlan.overview || 'Create the requested page'}`;

      // Add sections if available
      if (generatedPlan.sections && Array.isArray(generatedPlan.sections)) {
        comprehensivePrompt += `

📄 **PAGE SECTIONS:**`;
        generatedPlan.sections.forEach((section: string) => {
          comprehensivePrompt += `
• ${section}`;
        });
      }

      // Add features if available
      if (generatedPlan.features && Array.isArray(generatedPlan.features)) {
        comprehensivePrompt += `

⚡ **KEY FEATURES:**`;
        generatedPlan.features.forEach((feature: string) => {
          comprehensivePrompt += `
• ${feature}`;
        });
      }

      // Add accessibility requirements if available
      if (generatedPlan.accessibility && Array.isArray(generatedPlan.accessibility)) {
        comprehensivePrompt += `

♿ **ACCESSIBILITY REQUIREMENTS:**`;
        generatedPlan.accessibility.forEach((item: string) => {
          comprehensivePrompt += `
• ${item}`;
        });
      }

      // Add interactive elements instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🚀 Calling generateFromPrompt with comprehensive prompt:', {
        originalPrompt: newPagePrompt.substring(0, 100) + '...',
        comprehensivePromptLength: comprehensivePrompt.length,
        pageTitle: pageName,
        projectId,
        planSections: generatedPlan.sections?.length || 0,
        planFeatures: generatedPlan.features?.length || 0
      });

      await actions.generateFromPrompt(comprehensivePrompt, pageName);

      console.log('✅ generateFromPrompt completed successfully');

      // Reset remaining new page creation state
      setNewPagePrompt('');
      setGeneratedPlan(null);

      // Page will be automatically refreshed and switched when auto-save completes
      // via the pageSaved event listener

    } catch (error) {
      console.error('❌ Error creating page:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page: ${errorMessage}. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  // Handle plan rejection - go back to prompt editing
  const handleRejectPlan = () => {
    setShowPlanReview(false);
    setGeneratedPlan(null);

    actions.addMessage({
      role: 'assistant',
      content: `📝 Plan rejected. You can modify your prompt and try again.`,
      timestamp: new Date()
    });
  };

  // Cancel new page creation
  const handleCancelNewPage = () => {
    // Only set isCreatingNewPage to false if there are existing pages
    // If no pages exist, keep the prompt visible
    if (projectPages.length > 0) {
      setIsCreatingNewPage(false);
    }
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');

    actions.addMessage({
      role: 'assistant',
      content: `❌ New page creation cancelled.`,
      timestamp: new Date()
    });
  };

  // Handle page rename
  const handlePageRename = async (pageId: string, newName: string) => {
    if (!newName.trim()) return;

    try {
      // Call API to rename page
      const response = await fetch(`/api/page_gen/session/rename`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: pageId,
          newTitle: newName.trim()
        })
      });

      if (response.ok) {
        // Refresh the pages list to show updated name
        await refreshProjectPages();

        actions.addMessage({
          role: 'assistant',
          content: `✅ Page renamed to "${newName.trim()}"`,
          timestamp: new Date()
        });
      } else {
        throw new Error('Failed to rename page');
      }
    } catch (error) {
      console.error('Error renaming page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to rename page. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setRenamingPageId(null);
      setNewPageName('');
    }
  };

  // Handle page delete
  const handlePageDelete = async (pageId: string) => {
    try {
      // Call API to delete page
      const response = await fetch(`/api/page_gen/session/delete`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: pageId
        })
      });

      if (response.ok) {
        // If we're deleting the current page, clear the editor
        if (currentSessionId === pageId) {
          setCurrentSessionId(null);
          actions.setHtmlContent('');
          actions.setStableIframeContent('');

          // Clear current page ID in state to prevent infinite loops
          if (state.currentPageId === pageId) {
            // Reset to empty state instead of null
            actions.setHtmlContent('');
            actions.setStableIframeContent('');
          }
        }

        // Refresh the pages list
        await refreshProjectPages();

        // Check if this was the last page - if so, show create new page UI
        const updatedPages = projectPages.filter(p => p.id !== pageId);
        if (updatedPages.length === 0) {
          console.log('📄 Last page deleted, switching to create new page mode');
          setIsCreatingNewPage(true);
          setShowPlanReview(false);
          setNewPagePrompt('');
          setGeneratedPageName('');
        }

        actions.addMessage({
          role: 'assistant',
          content: `✅ Page deleted successfully`,
          timestamp: new Date()
        });
      } else {
        throw new Error('Failed to delete page');
      }
    } catch (error) {
      console.error('Error deleting page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to delete page. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setShowDeleteConfirm(null);
    }
  };

  // Start rename process
  const startRename = (page: any) => {
    console.log('🔄 startRename called with page:', page);
    setRenamingPageId(page.id);
    setNewPageName(page.title || `Page ${page.id}`);
    setOpenDropdownId(null);
  };

  // Cancel rename
  const cancelRename = () => {
    setRenamingPageId(null);
    setNewPageName('');
  };

  // Confirm rename
  const confirmRename = (pageId: string) => {
    handlePageRename(pageId, newPageName);
  };

  // Start delete process
  const startDelete = (pageId: string) => {
    console.log('🗑️ startDelete called with pageId:', pageId);
    setShowDeleteConfirm(pageId);
    setOpenDropdownId(null);
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  // Confirm delete
  const confirmDelete = (pageId: string) => {
    handlePageDelete(pageId);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId && event.target) {
        // Check if the click is outside the dropdown menu
        const target = event.target as Element;
        const dropdown = target.closest('.dropdown-menu');
        const dropdownButton = target.closest('.dropdown-button');

        // Don't close if clicking inside dropdown or on dropdown button
        if (!dropdown && !dropdownButton) {
          console.log('🔄 Closing dropdown due to outside click');
          setOpenDropdownId(null);
        }
      }
    };

    if (openDropdownId) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [openDropdownId]);

  // Generate page name when prompt changes using the new service
  useEffect(() => {
    if (newPagePrompt.trim()) {
      // Add a small delay to debounce rapid changes
      const timeoutId = setTimeout(async () => {
        try {
          const result = await createPageWithCleanName(
            { prompt: newPagePrompt },
            'new-page-flow'
          );

          if (result.success && result.pageName) {
            setGeneratedPageName(result.pageName);
          } else {
            console.warn('Page name generation failed:', result.error);
            setGeneratedPageName('New Page'); // Fallback
          }
        } catch (error) {
          console.error('❌ Error generating page name:', error);
          setGeneratedPageName('New Page'); // Fallback
        }
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
    } else {
      setGeneratedPageName('');
    }
  }, [newPagePrompt]);

  // Reset isCreatingNewPage when content is available
  useEffect(() => {
    if ((state.htmlContent || state.stableIframeContent) && isCreatingNewPage && !state.isGenerating) {
      console.log('🔄 Content available, resetting isCreatingNewPage');
      setIsCreatingNewPage(false);
    }
  }, [state.htmlContent, state.stableIframeContent, isCreatingNewPage, state.isGenerating]);

  // Enhancement on submit (moved from auto-typing trigger)
  const triggerEnhancement = async (message: string) => {
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    if (!hasContent || message.length < 10) return false;

    setIsEnhancing(true);
    try {
      const htmlContent = state.htmlContent || state.stableIframeContent;
      const pageId = state.currentPageId || currentSessionId;

      // Extract element selector parameters
      const editParams = extractEditParameters(selectedElement);

      // 🚀 OPTIMIZATION: Use pageId if available, otherwise use htmlContent
      const enhancementResult = await previewEnhancement(
        message,
        htmlContent,
        pageId?.toString(),
        editParams.elementSelector,
        editParams.fragmentHtml
      );
      if (enhancementResult && enhancementResult.confidence !== 'low') {
        setEnhancement(enhancementResult);
        setShowEnhancement(true);
        setOriginalMessage(message); // Store original message since input is cleared
        return true; // Enhancement shown, wait for user choice
      }
    } catch (error) {
      console.error('Enhancement preview failed:', error);
    } finally {
      setIsEnhancing(false);
    }
    return false; // No enhancement, proceed normally
  };

  const handleLinkPages = async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);

    if (pagesWithContent.length < 2) {
      actions.addMessage({
        role: 'assistant',
        content: '⚠️ Need at least 2 pages with content to link navigation',
        timestamp: new Date()
      });
      return;
    }

    // Prevent multiple linking operations
    if (linkingProgress) {
      console.log('🔗 Linking already in progress, ignoring duplicate call');
      return;
    }

    // Close linking dialog if open
    setShowLinkingDialog(false);

    // Initialize progress tracking
    setLinkingProgress({
      current: 0,
      total: pagesWithContent.length,
      currentPage: 'Starting...'
    });

    actions.addMessage({
      role: 'assistant',
      content: `🔗 Linking ${pagesWithContent.length} pages with navigation...`,
      timestamp: new Date()
    });

    try {
      // Use the improved linking with progress callback
      await linkPagesWithProgress(pagesWithContent);

      actions.addMessage({
        role: 'assistant',
        content: '✅ All pages have been linked with navigation!',
        timestamp: new Date()
      });

      // Reset linking suggestion state so it can show again for new pages
      setHasShownLinkingSuggestion(false);
    } catch (error) {
      console.error('Linking failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: '❌ Failed to link some pages. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setLinkingProgress(null);
    }
  };

  // CLIENT-SIDE LINKING: Fast, immediate, no API calls needed
  const linkPagesWithProgress = async (pages: any[]) => {
    console.log(`🔗 Starting CLIENT-SIDE linking for ${pages.length} pages`);

    // Process all pages immediately on client-side
    pages.forEach((page, index) => {
      const otherPageNames = pages
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      console.log(`🔗 Client-side linking page ${index + 1}/${pages.length}: ${page.name}`);

      // Update progress for this page
      setLinkingProgress({
        current: index + 1,
        total: pages.length,
        currentPage: page.name
      });

      try {
        // Parse the HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(page.content, 'text/html');

        // Find navigation area (try multiple selectors)
        let navElement = doc.querySelector('nav') ||
          doc.querySelector('.nav') ||
          doc.querySelector('.navigation') ||
          doc.querySelector('header nav') ||
          doc.querySelector('.header nav');

        if (!navElement) {
          // If no nav found, try to find header and add nav there
          const header = doc.querySelector('header') || doc.querySelector('.header');
          if (header) {
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            header.appendChild(navElement);
          } else {
            // Create a simple nav at the top of body
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            navElement.style.cssText = 'padding: 1rem; background: #f8f9fa; border-bottom: 1px solid #dee2e6;';
            doc.body.insertBefore(navElement, doc.body.firstChild);
          }
        }

        // Clear existing auto-generated navigation links (but keep original nav content)
        const existingAutoLinks = navElement.querySelectorAll('a[data-page-link="true"]');
        existingAutoLinks.forEach(link => link.remove());

        // Also remove any links that match other page names (to prevent duplicates)
        const allLinks = navElement.querySelectorAll('a');
        allLinks.forEach(link => {
          const linkText = link.textContent?.trim().toLowerCase();
          const isPageLink = otherPageNames.some(pageName => {
            const normalizedPageName = pageName.toLowerCase();
            return linkText === normalizedPageName ||
              linkText === (normalizedPageName + ' page') ||
              linkText === normalizedPageName.replace(' page', '') ||
              (linkText + ' page') === normalizedPageName;
          });
          if (isPageLink) {
            link.remove();
          }
        });

        // Add links to other pages with proper navigation attributes
        otherPageNames.forEach(pageName => {
          const link = doc.createElement('a');
          link.href = '#';

          // Use the clean page name for display (remove " Page" suffix if present)
          const displayName = pageName.endsWith(' Page') ? pageName.replace(' Page', '') : pageName;
          link.textContent = displayName;

          // Use data-nav attribute for proper navigation (this is what the system expects)
          const pageId = pageName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .replace(/^-+|-+$/g, '');

          link.setAttribute('data-nav', pageId);
          link.setAttribute('data-page-link', 'true'); // For our tracking
          link.style.cssText = 'margin-right: 1rem; color: #007bff; text-decoration: none; cursor: pointer;';

          navElement.appendChild(link);
        });

        // Ensure the page has proper SPA router script for navigation
        let scriptElement = doc.querySelector('script[data-exec="inline"]');
        if (!scriptElement) {
          scriptElement = doc.createElement('script');
          scriptElement.setAttribute('data-exec', 'inline');
          scriptElement.textContent = `
// SPA Router for page navigation
document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    const pageName = target.textContent.trim();

    console.log('🔗 Navigation click in iframe:', {
      pageId,
      pageName,
      targetElement: target.outerHTML
    });

    // Send message to parent to switch pages
    if (window.parent && window.parent.postMessage) {
      window.parent.postMessage({
        type: 'NAVIGATE_TO_PAGE',
        pageId: pageId,
        pageName: pageName
      }, '*');
    }
  }
});
          `;
          doc.body.appendChild(scriptElement);
        }

        // Update the page content with modified HTML
        const updatedHtml = doc.documentElement.outerHTML;
        actions.updatePage(page.id, { content: updatedHtml });
        console.log(`✅ Client-side linking completed for ${page.name}`);

      } catch (error) {
        console.error(`❌ Failed to update page ${page.name}:`, error);
      }
    });

    // No need to wait for API calls - everything is done immediately!
    console.log('🔗 All pages linked instantly via client-side manipulation');
  };

  // ============================================================================
  // SPASHELL INTEGRATION FUNCTIONS
  // ============================================================================

  // Toggle between PreviewPanel and SPAShell modes
  const toggleSPAMode = () => {
    setUseSPAMode(!useSPAMode);
    console.log(`🔄 Switched to ${!useSPAMode ? 'SPAShell' : 'PreviewPanel'} mode`);
  };

  // Handle SPAShell edit mode toggle
  const handleSPAEditModeToggle = () => {
    setSpaEditMode(!spaEditMode);
    console.log(`🔄 SPAShell edit mode: ${!spaEditMode ? 'enabled' : 'disabled'}`);
  };

  // SPAShell element click handler - integrates with existing element handling
  const handleSPAElementClick = async (element: any) => {
    console.log('🔥 SPAShell element clicked:', element);

    // Use the same element handling logic as PreviewPanel
    await handleElementClick(element);
  };

  // SPAShell navigation handler - integrates with existing navigation handling
  const handleSPANavigationClick = async (element: any) => {
    console.log('🔥 SPAShell navigation clicked:', element);

    // Use the same navigation handling logic as PreviewPanel
    await handleNavigationClick(element);
  };

  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    let htmlFragment = '';

    // First, check if this is already a complete HTML document
    if (response.trim().startsWith('<!DOCTYPE html') || response.trim().startsWith('<html')) {
      return response.trim();
    }

    // Look for HTML content between ```html and ``` markers
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      htmlFragment = htmlMatch[1].trim();
    } else if (response.includes('<') && response.includes('>')) {
      // If response contains HTML tags, assume it's HTML fragment
      const firstTagIndex = response.indexOf('<');
      htmlFragment = response.substring(firstTagIndex).trim();
    } else {
      return response;
    }

    // If it's a fragment (doesn't start with DOCTYPE or html), wrap it in parent structure
    if (htmlFragment && !htmlFragment.startsWith('<!DOCTYPE') && !htmlFragment.startsWith('<html')) {
      return createParentHtmlStructure(htmlFragment);
    }

    return htmlFragment;
  };

  // Create parent HTML structure with Tailwind CSS for fragments
  const createParentHtmlStructure = (fragment: string): string => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Event delegation for data-action attributes
    document.addEventListener('DOMContentLoaded', function() {
      document.addEventListener('click', function(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
          const action = target.getAttribute('data-action');
          const targetId = target.getAttribute('data-target');

          switch(action) {
            case 'openModal':
              if (targetId) openModal(targetId);
              break;
            case 'closeModal':
              if (targetId) closeModal(targetId);
              break;
            default:
              console.log('Action triggered:', action, 'Target:', targetId);
          }
        }
      });
    });

    // Modal functions
    function openModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
      }
    }

    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
      }
    }
  </script>
</head>
<body>
  <div id="app">
    ${fragment}
  </div>
</body>
</html>`;
  };

  const handleImplementChoice = async (choice: 'inline' | 'modal' | 'page') => {
    console.log('🔥 handleImplementChoice called with:', choice);
    console.log('🔥 selectedElement:', state.selectedElement);
    console.log('🔥 isGenerating:', state.isGenerating);

    if (!state.selectedElement) {
      console.log('🔥 No selected element, returning');
      return;
    }

    // Prevent multiple implementation calls
    if (state.isGenerating) {
      console.log('🔥 Implementation already in progress, ignoring');
      return;
    }

    console.log('🔥 Closing implementation modal');

    // CRITICAL FIX: Capture selected element data BEFORE clearing state
    const selectedElementData = state.selectedElement;
    const elementText = selectedElementData?.textContent || 'element';
    const elementType = selectedElementData?.implementationType || 'interactive';

    console.log('🔍 Captured element data before state clearing:', {
      hasElement: !!selectedElementData,
      elementText,
      elementType,
      hasOuterHTML: !!selectedElementData?.outerHTML
    });

    actions.setShowImplementModal(false);

    // Auto-disable edit mode after implementation choice
    if (spaEditMode) {
      console.log('🔥 Auto-disabling edit mode after implementation');
      setSpaEditMode(false);
    }

    // Clear custom functionality after implementation
    actions.setCustomFunctionality('');

    // Use custom functionality if provided, otherwise use default description
    const functionalityDescription = state.customFunctionality.trim() ||
      `Implement "${elementText}" functionality`;

    // Add user message showing their choice
    actions.addMessage({
      role: 'user',
      content: `${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : choice === 'modal' ? 'a modal/popup' : 'a new page'}`,
      timestamp: new Date()
    });

    if (choice === 'page') {
      // Handle page creation aligned with main page creation workflow
      try {
        const result = await createPageWithCleanName(
          { prompt: elementText },
          'element-implementation'
        );

        if (!result.success) {
          throw new Error(result.error || 'Failed to create page');
        }

        const { pageName, pageId } = result;

        // Check if page already exists
        const existingPage = state.pages.find(p => p.id === pageId);
        if (existingPage) {
          actions.switchToPage(pageId!);
          actions.addMessage({
            role: 'assistant',
            content: `✅ Switched to existing "${pageName}" page`,
            timestamp: new Date()
          });
          return;
        }

        // Clear current page selection to prepare for new page creation
        setCurrentSessionId(null);
        actions.setHtmlContent('');
        actions.setStableIframeContent('');

        // Add assistant message about page creation
        actions.addMessage({
          role: 'assistant',
          content: `🚀 Creating "${pageName}" page for this feature...`,
          timestamp: new Date()
        });

        // Generate content using the same pattern as main page creation
        // This will auto-save the page after HTML generation completes
        const prompt = generatePageContentPrompt(pageName!, state.pages);
        await actions.generateFromPrompt(prompt, pageName!);

        // Page will be automatically refreshed and switched when auto-save completes
        // via the pageSaved event listener

        console.log('🔗 Element-based page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Error creating page:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to create page. Please try again.`,
          timestamp: new Date()
        });
      }
    } else {
      // Handle inline or modal implementation using editContent (includes conversation history)
      try {
        // Create intent and user messages to include in conversation history
        const additionalMessages: ChatMessage[] = [];

        // Add intent message if available
        if (selectedElementData?.intentData) {
          additionalMessages.push({
            role: 'assistant',
            content: `🎯 **Intent Analysis:**\n${selectedElementData.intentData.userIntent}\n\n💡 **Suggestion:**\n${selectedElementData.intentData.suggestion || "I can help implement this functionality."}`,
            timestamp: new Date()
          });
        }

        // Add user message
        const userMessage: ChatMessage = {
          role: 'user',
          content: `${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}`,
          timestamp: new Date()
        };
        additionalMessages.push(userMessage);

        // Add messages to chat UI
        additionalMessages.forEach(msg => actions.addMessage(msg));

        // Create implementation prompt that references the intent analysis
        const intentContext = selectedElementData?.intentData
          ? `Based on the intent analysis above: "${selectedElementData.intentData.userIntent}"

${selectedElementData.intentData.suggestion || "Please implement this functionality."}

` : '';

        // Use custom functionality description if provided
        const customDescription = state.customFunctionality.trim()
          ? `\n\nUser's specific requirements: "${state.customFunctionality}"\n`
          : '';

        // Determine if this is a rename/replace operation
        const isRenameOperation = state.customFunctionality.toLowerCase().includes('rename') ||
          state.customFunctionality.toLowerCase().includes('change to') ||
          state.customFunctionality.toLowerCase().includes('replace with');

        const implementationPrompt = `${intentContext}${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}.${customDescription}

Context: User clicked on an element and wants to implement functionality. Please modify the existing content ${isRenameOperation ? 'by REPLACING/RENAMING the clicked element' : 'to add this feature'} while preserving the current design and layout.

Implementation type: ${choice}
Element type: ${elementType}
Selected element: "${elementText}"

${choice === 'modal' ? 'Create a modal/popup that opens when the element is clicked.' : isRenameOperation ? 'REPLACE the selected element with the new functionality - do not add new elements.' : 'Add the functionality directly to the current page.'}

Important: ${isRenameOperation ? 'This is a REPLACEMENT operation - modify the existing "' + elementText + '" element, do not create new elements.' : state.customFunctionality.trim() ? 'Follow the user\'s specific requirements above exactly.' : 'The intent analysis and suggestion above should guide your implementation approach.'}`;

        // Use editContent with explicit conversation history including intent
        // Pass element selector, implementation type, and captured element data for better targeting
        await actions.editContent(implementationPrompt, additionalMessages, selectedElementData?.tagName?.toLowerCase(), choice, selectedElementData);

        // After successful edit, reload the page content from database to ensure consistency
        if (currentSessionId) {
          console.log('🔄 Reloading page content after successful implementation...');
          const reloadSuccess = await actions.reloadCurrentPage(currentSessionId);
          if (reloadSuccess) {
            console.log('✅ Page content reloaded successfully after implementation');
          } else {
            console.warn('⚠️ Failed to reload page content after implementation');
          }
        }

        actions.addMessage({
          role: 'assistant',
          content: `✅ Successfully implemented "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}!`,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Implementation error:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to implement "${elementText}". Please try again.`,
          timestamp: new Date()
        });
      }
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatPlanForDisplay = (plan: any): string => {
    if (typeof plan === 'string') {
      return plan;
    }

    if (plan && typeof plan === 'object') {
      let planContent = '';

      // Add overview
      if (plan.overview) {
        planContent += `${plan.overview}\n\n`;
      }

      // Add sections
      if (plan.sections && Array.isArray(plan.sections)) {
        plan.sections.forEach((section: any, index: number) => {
          planContent += `${index + 1}. ${section.title}\n`;
          if (section.description) {
            planContent += `${section.description}\n`;
          }
          if (section.details && Array.isArray(section.details)) {
            section.details.forEach((detail: string) => {
              planContent += `• ${detail}\n`;
            });
          }
          planContent += '\n';
        });
      }

      // Add features
      if (plan.features && Array.isArray(plan.features)) {
        planContent += `Key Features:\n`;
        plan.features.forEach((feature: string) => {
          planContent += `• ${feature}\n`;
        });
        planContent += '\n';
      }

      // Add accessibility
      if (plan.accessibility && Array.isArray(plan.accessibility)) {
        planContent += `Accessibility:\n`;
        plan.accessibility.forEach((item: string) => {
          planContent += `• ${item}\n`;
        });
      }

      return planContent.trim();
    }

    return '';
  };



  // All page creation utility functions are now handled by the new services

  // ============================================================================
  // RENDER
  // ============================================================================

  // Show project selector if no project is specified
  if (!projectId) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-4xl w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Editor</h1>
              <p className="text-gray-600">Select a project to open in the editor</p>
            </div>

            {isLoadingProjects ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-gray-600">Loading projects...</span>
              </div>
            ) : availableProjects.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                <p className="text-gray-600 mb-6">Create your first project to get started</p>
                <button
                  onClick={() => navigate('/prototypes')}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Go to My Prototypes
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableProjects.map((project) => (
                  <div
                    key={project.id}
                    onClick={() => handleProjectSelect(project.id)}
                    className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors"
                  >
                    <h3 className="font-medium text-gray-900 mb-2">{project.title}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {project.description || 'No description'}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Updated {new Date(project.updated_at).toLocaleDateString()}</span>
                      <span className="px-2 py-1 bg-gray-100 rounded text-gray-700">{project.status}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-8 text-center">
              <button
                onClick={() => navigate('/prototypes')}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                ← Back to My Prototypes
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }  return (
    <ErrorBoundary>
      {/* Modern 3-Column AI Chat Layout */}
      <div className="h-[84vh] flex flex-col">
        <div className="h-full flex flex-row relative">
          {/* Toggle button positioned outside the collapsible area */}
          <button
            onClick={() => setIsLeftPaneCollapsed(!isLeftPaneCollapsed)}
            className="absolute top-4 left-4 z-50 p-2 bg-white rounded-md border border-gray-200 shadow-sm flex justify-center hover:bg-gray-50"
            style={{ transform: isLeftPaneCollapsed ? 'translateX(0)' : 'translateX(0)' }}
          >
            <Bars3Icon className={`w-5 h-5 ${isLeftPaneCollapsed ? '' : 'rotate-180'}`} />
          </button>
          
          {/* Left Panel (Sidebar) - Using shared PageSidebar component */}
          <PageSidebar
            isOpen={!isLeftPaneCollapsed}
            onClose={() => setIsLeftPaneCollapsed(true)}
            project={projectId ? { id: projectId, title: '', status: 'active', type: 'prototype', created_at: '', updated_at: '' } : null}
            pages={projectPages}
            currentPageId={currentSessionId}
            isLoadingPages={isLoadingProjectPages}
            isLoadingPage={isLoadingPage}
            onPageSelect={handleProjectPageSelect}
            onCreatePage={handleCreateNewPage}
            onPageRename={handlePageRenameShared}
            onPageDelete={handlePageDeleteShared}
            onLinkPages={handleLinkPages}
            theme="violet"
            variant="inline"
            showNewPageButton={true}
            showPageActions={true}
          />          {/* Center Panel (Main Canvas) - Full height during generation */}
          <div className={`flex-1 flex flex-col bg-gray-50 ${isLeftPaneCollapsed ? 'w-full' : ''} h-full`}>
            {/* Main Content Area */}
            <div className={`flex-1 flex ${state.isGenerating ? 'p-0' : 'items-center justify-center p-6'} overflow-hidden`}>
              <div className={`w-full ${isLeftPaneCollapsed ? 'w-full' : state.isGenerating ? 'w-full' : 'max-w-4xl'} ${state.isGenerating ? 'h-full' : 'bg-white rounded-2xl shadow-xl border border-gray-200 max-h-[calc(100vh-160px)]'} flex flex-col`}>
                {/* Content based on current state */}                {(isCreatingNewPage || (projectId && projectPages.length === 0 && !isLoadingProjectPages)) && !state.isGenerating ? (
                  /* New Page Creation UI */
                  <div className="p-8 overflow-y-auto">
                    {isGeneratingPlan ? (
                      /* Plan Generation Loading */
                      <div className="space-y-6 text-center">
                        <div className="text-center">
                          <h2 className="text-xl font-semibold text-gray-900 mb-2">Generating Plan</h2>
                          <p className="text-gray-600">Page: {generatedPageName}</p>
                        </div>

                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-16 h-16 border-4 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                          <div className="text-center">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating Your Plan</h3>
                            <p className="text-gray-600">Analyzing your requirements and crafting a detailed implementation strategy...</p>
                          </div>
                        </div>
                      </div>
                    ) : showPlanReview ? (
                      /* Plan Review - Structured like PlanReviewPageV3 */
                      <div className="h-full overflow-y-auto">
                        {/* Header */}
                        <div className="bg-white border-b border-gray-200 p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <h1 className="text-2xl font-bold text-gray-900">Design Plan</h1>
                              <p className="text-gray-600 text-sm mt-1">
                                Review the detailed implementation plan
                                {generatedPageName && (
                                  <span className="ml-2">• Page: <span className="font-medium text-blue-600">{generatedPageName}</span></span>
                                )}
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-700">
                                🖥️ Desktop
                              </span>
                              <button
                                onClick={handleGeneratePageFromPlan}
                                disabled={state.isGenerating}
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              >
                                {state.isGenerating ? (
                                  <>
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                    Generating...
                                  </>
                                ) : (
                                  <>
                                    Generate
                                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  </>
                                )}
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="p-6">
                          <PlanDisplay planData={generatedPlan} />
                        </div>

                        {/* Bottom Actions */}
                        <div className="bg-white border-t border-gray-200 p-6">
                          <div className="flex space-x-3">
                            <button
                              onClick={handleRejectPlan}
                              className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                            >
                              Edit Prompt
                            </button>
                            <button
                              onClick={handleGeneratePageFromPlan}
                              disabled={state.isGenerating}
                              className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                            >
                              {state.isGenerating ? (
                                <>
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                  Generating...
                                </>
                              ) : (
                                'Generate Page'
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      /* Prompt Input */
                      <div className="space-y-6">
                        <div className="text-center">
                          <h2 className="text-xl font-semibold text-gray-900 mb-2">Create New Page</h2>
                          <p className="text-gray-600">Describe what kind of page you'd like to create</p>
                        </div>

                        <PromptInput
                          value={newPagePrompt}
                          onChange={setNewPagePrompt}
                          onSubmit={(value) => {
                            setNewPagePrompt(value);
                            handleNewPagePromptSubmit();
                          }}
                          placeholder="Describe your page... (e.g., 'Create a modern login form with email and password fields')"
                          submitButtonText="Create Plan"
                          loadingText="Generating Plan..."
                          height="h-32"
                          theme="violet"
                          isLoading={isGeneratingPlan}
                          variant="inline"
                          showSubmitButton={false}
                          additionalContent={
                            <div className="space-y-4">
                              {generatedPageName && (
                                <div className="text-sm text-gray-600">
                                  Page name: <span className="font-medium">{generatedPageName}</span>
                                </div>
                              )}

                              <div className="flex space-x-3">
                                {projectPages.length > 0 && (
                                  <button
                                    onClick={handleCancelNewPage}
                                    className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                                  >
                                    Cancel
                                  </button>
                                )}
                                <button
                                  onClick={handleNewPagePromptSubmit}
                                  disabled={!newPagePrompt.trim() || isGeneratingPlan}
                                  className="flex-1 px-4 py-3 bg-violet-600 text-white rounded-xl hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                                >
                                  {isGeneratingPlan ? (
                                    <>
                                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                      Generating Plan...
                                    </>
                                  ) : (
                                    'Create Plan'
                                  )}
                                </button>
                              </div>
                            </div>
                          }
                        />
                      </div>
                    )}
                  </div>
                ) :  state.isGenerating ? (
                  /* Progressive Generation Rendering - Full height, no borders */
                  <div className="h-full relative bg-gray-50">
                    {/* Progressive Content Container */}
                    <div className="h-full overflow-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:bg-transparent [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300/60 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400/60">
                      <div className="p-6" style={{height: '100%'}}>
                        {(() => {
                          // Clean the streaming content
                          const rawContent = state.streamingContent || '';
                          const cleanContent = rawContent
                            .replace(/```html\s*/g, '')
                            .replace(/```\s*/g, '')
                            .trim();

                          console.log('🎬 Progressive rendering state:', {
                            isGenerating: state.isGenerating,
                            hasStreamingContent: !!state.streamingContent,
                            streamingContentLength: state.streamingContent?.length || 0,
                            cleanContentLength: cleanContent.length,
                            cleanContentPreview: cleanContent.substring(0, 200) + '...',
                            willShowProgressiveContent: cleanContent.length > 0,
                            rawContentPreview: rawContent.substring(0, 100) + '...'
                          });

                          // Always show progressive content if we have any content
                          if (cleanContent.length > 0) {
                            return (
                              <div className="h-full flex flex-col">
                                {/* Progressive Content Rendering - Full height */}
                                <div className="flex-1 bg-white">
                                  <SeamlessProgressiveRenderer
                                    htmlContent=""
                                    streamingContent={cleanContent}
                                    isGenerating={true}
                                    onElementClick={handleElementClick}
                                    className="w-full h-full"
                                  />
                                </div>

                                {/* Generation Progress Message - Fixed at bottom */}
                                <div className="flex-shrink-0 bg-blue-50 border-t border-blue-200 p-3">
                                  <div className="flex items-center space-x-2">
                                    <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                                    <span className="text-sm font-medium text-blue-800">
                                      {generationProgress?.message || 'Generating more content...'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            );
                          } else {
                            // Show loading state for initial generation
                            return (
                              <div className="space-y-4">
                                <div className="text-center py-8">
                                  <div className="w-12 h-12 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating Your Page</h3>
                                  <p className="text-gray-600">
                                    {generationProgress?.message || 'Starting generation...'}
                                  </p>
                                  {generationProgress && (
                                    <p className="text-sm text-gray-500 mt-2">
                                      {Math.floor((Date.now() - generationProgress.startTime) / 1000)}s elapsed
                                    </p>
                                  )}
                                </div>
                              </div>
                            );
                          }
                        })()}
                      </div>
                    </div>

                    
                  </div>          
                        ) : state.htmlContent || state.stableIframeContent || state.isGenerating ? (
                  <div className="h-full overflow-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:bg-transparent [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300/60 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400/60">
                    <SPAShell
                      ref={spaShellRef}
                      className="h-full p-4"
                      enableEditMode={spaEditMode}
                      dashboardHtml={(() => {
                        const rawContent = state.htmlContent || state.stableIframeContent || '';
                        const cleanContent = rawContent
                          .replace(/```html\s*/g, '')
                          .replace(/```\s*/g, '')
                          .trim();
                        return cleanContent || 'No content available';
                      })()}
                      streamingContent={state.streamingContent || ''}
                      isGenerating={state.isGenerating}
                      onElementClick={useSPAMode ? handleElementClick : undefined}
                      viewMode={viewMode}
                      useSPAMode={useSPAMode}
                    />
                  </div>
                ) : (
                  /* Empty State */
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Page Selected</h3>
                    <p className="text-gray-600">Select a page from the sidebar or create a new one</p>
                  </div>
                )}
              </div>

              {/* VERSIONING DISABLED: Commented out Prototype Version Switcher */}
              {/*
              {(() => {
                console.log('🔍 Version switcher visibility check:', {
                  projectId,
                  versionLabelsLength: versionLabels.length,
                  versionLabels,
                  hasFirstVersion,
                  shouldShow: projectId && versionLabels.length > 0
                });
                return projectId && versionLabels.length > 0;
              })() && (
                <div className="absolute bottom-6 left-6 z-10">
                  <PrototypeVersionSwitcher
                    versions={versionLabels}
                    currentVersion={currentVersionLabel}
                    onVersionChange={(versionLabel) => {
                      console.log('🔄 Version switcher clicked:', versionLabel);
                      setIsVersionSwitching(true);
                      switchToVersion(versionLabel);
                    }}
                    isLoading={isVersionLoading || isVersionCreationInProgress}
                  />

                  {isVersionCreationInProgress && (
                    <div className="mt-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg backdrop-blur-sm">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin h-3 w-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                        <span className="text-blue-700 text-xs font-medium">Creating version...</span>
                      </div>
                    </div>
                  )}

                  {isVersionSwitching && (
                    <div className="mt-2 px-3 py-1 bg-green-50 border border-green-200 rounded-lg backdrop-blur-sm">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin h-3 w-3 border-2 border-green-500 border-t-transparent rounded-full"></div>
                        <span className="text-green-700 text-xs font-medium">Switching version...</span>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {versionError && (
                <div className="absolute bottom-20 left-6 z-10 max-w-sm">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 shadow-lg backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                      <div className="text-red-600 text-sm">{versionError}</div>
                      <button
                        onClick={clearVersionError}
                        className="ml-2 text-red-500 hover:text-red-700"
                        title="Dismiss error"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                </div>
              )}
              */
            </div>

            {/* Floating Bottom Toolbar - Subtle Modern Design */}
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
              <div className="flex items-center backdrop-blur-sm bg-white/80 rounded-full shadow-sm px-1.5 py-1 border border-gray-100 transition-all duration-200 hover:shadow-md">
                {/* Code/Preview Toggle Button */}
                <button
                  onClick={() => setViewMode(viewMode === 'code' ? 'preview' : 'code')}
                  className={`flex items-center p-2 rounded-full transition-all duration-200 ${viewMode === 'code'
                    ? 'bg-violet-500/10 text-violet-600'
                    : 'text-gray-500 hover:bg-gray-100/70'
                  }`}
                  title={viewMode === 'code' ? 'Switch to Preview' : 'Switch to Code'}
                >
                  {viewMode === 'code' ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                  )}
                </button>

                {/* Divider */}
                <div className="w-px h-6 bg-gray-200 mx-1"></div>

                {/* Edit Mode Button - Only show in preview mode */}
                {viewMode === 'preview' && (
                  <button
                    onClick={handleSPAEditModeToggle}
                    className={`flex items-center p-2 rounded-full transition-all duration-200 ${
                      spaEditMode
                        ? 'bg-red-500/10 text-red-600'
                        : 'text-gray-500 hover:bg-gray-100/70'
                    }`}
                    title={spaEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                )}

                
              </div>
            </div>

            
          </div>          {/* Right Panel (AI Chat) - Fixed ~20rem width */}
          <div className="w-96 bg-white border-l border-gray-200 flex flex-col h-full">
            {/* Chat Header */}
            {/* <div className="flex-shrink-0 p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
              <p className="text-sm text-gray-600 mt-1">
                Describe what you'd like to create or modify
              </p>
            </div> */}

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {state.messages.map((message, index) => (
                <div key={index}>
                  {message.role === 'user' ? (
                    /* User Message */
                    <div className="flex justify-end">
                      <div className="max-w-[80%] bg-violet-600 text-white rounded-2xl rounded-br-md px-4 py-3">
                        <div className="text-sm leading-relaxed whitespace-pre-wrap">
                          {message.content}
                        </div>
                        <div className="text-xs text-violet-100 mt-1 opacity-75">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Assistant Message */
                    <div className="flex justify-start">
                      <div className="max-w-[80%] bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                        {message.type === 'plan' ? (
                          <div className="text-sm leading-relaxed">
                            {/* Implementation Plan Header */}
                            <div className="flex items-center space-x-2 mb-3">
                              <div className="w-4 h-4 text-orange-500">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="font-medium text-gray-900">Implementation Plan</span>
                            </div>

                            {/* Plan Content */}
                            <div className="text-gray-700 text-sm leading-relaxed">
                              {message.content.split('\n').map((line, lineIndex) => {
                                const trimmed = line.trim();
                                if (!trimmed) return null;

                                // Skip markdown headers and formatting
                                if (trimmed.startsWith('#') || trimmed.startsWith('**') || trimmed.startsWith('📋') || trimmed.startsWith('🎯')) {
                                  return null;
                                }

                                // Handle bullet points
                                if (trimmed.startsWith('•') || trimmed.startsWith('-')) {
                                  return (
                                    <div key={lineIndex} className="ml-4 mb-1">
                                      • {trimmed.replace(/^[•-]\s*/, '')}
                                    </div>
                                  );
                                }

                                // Handle numbered sections
                                if (/^\d+\.\s/.test(trimmed)) {
                                  return (
                                    <div key={lineIndex} className="font-medium text-gray-900 mt-3 mb-1">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                // Handle section headers
                                if (trimmed.endsWith(':') && trimmed.length < 30) {
                                  return (
                                    <div key={lineIndex} className="font-medium text-gray-900 mt-3 mb-1">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                // Regular text
                                if (trimmed.length > 10) {
                                  return (
                                    <div key={lineIndex} className="mb-2">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                return null;
                              }).filter(Boolean)}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
                            {message.content}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Typing Indicator */}
              {state.isGenerating && (
                <div className="flex justify-start">
                  <div className="bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                    <div className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              )}            </div>

            {/* Element Selector Controls - Prominent placement */}
            <div className="flex-shrink-0 p-4 border-t border-gray-100">
              <EditModeControls
                isEditModeActive={isEditModeActive}
                selectedElement={selectedElement}
                onToggleEditMode={() => elementSelectorManager.toggleEditMode()}
                onClearSelection={() => elementSelectorManager.clearSelection()}
                onExitEditMode={() => elementSelectorManager.exitEditMode()}
              />
            </div>

            {/* Chat Input */}
            <div className="flex-shrink-0 p-4 border-t border-gray-100">
              {/* Enhancement Preview */}
              {showEnhancement && enhancement && (
                <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span className="text-sm font-medium text-blue-800">Enhanced Prompt</span>
                      <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                        enhancement.confidence === 'high' ? 'bg-green-100 text-green-800' :
                        enhancement.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {enhancement.confidence} confidence
                      </span>
                    </div>
                    <button onClick={handleCloseEnhancement} className="text-gray-400 hover:text-gray-600">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="text-sm text-gray-700 mb-2">
                    <strong>Analysis:</strong> {enhancement.analysisType}
                    {enhancement.gridChanges && (
                      <div className="mt-1">
                        <strong>Grid Changes:</strong> {enhancement.gridChanges}
                      </div>
                    )}
                  </div>

                  <div className="text-sm text-gray-600 mb-3 max-h-20 overflow-y-auto">
                    {enhancement.enhancedPrompt}
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={handleUseEnhanced}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      Use Enhanced
                    </button>
                    <button
                      onClick={handleCloseEnhancement}
                      className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
                    >
                      Use Original
                    </button>
                  </div>
                </div>
              )}

              {/* Enhancement Loading Indicator */}
              {isEnhancing && (
                <div className="flex items-center text-sm text-blue-600 mb-2">
                  <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Analyzing prompt...
                </div>
              )}

              <PromptInput
                value={state.input}
                onChange={actions.setInput}
                onSubmit={(value) => {
                  if (value.trim() && !state.isGenerating) {
                    handleChatSubmit(value.trim());
                  }
                }}
                placeholder={state.isGenerating ? "AI is working..." : "Tell me what to change, specific and clear. One task at a time."}
                submitButtonText=""
                loadingText=""
                height="min-h-[100px] max-h-[200px]"
                theme="violet"
                isLoading={state.isGenerating}
                variant="chat"
                showSubmitButton={true}
                additionalContent={
                  <div className="flex justify-start mt-3">
                    <button
                      type="button"
                      onClick={() => setElementSelectorActive(!elementSelectorActive)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium transition-colors ${elementSelectorActive
                        ? 'bg-violet-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                      </svg>
                      <span>Selector</span>
                    </button>
                  </div>
                }
              />
            </div>
          </div>
        </div>
        {/* Modal Dialogs */}
        <ModalDialogs
          showPageCreationDialog={showPageCreationDialog}
          pendingPageCreation={pendingPageCreation}
          onConfirmPageCreation={confirmPageCreation}
          onCancelPageCreation={cancelPageCreation}
          showLinkingDialog={showLinkingDialog}
          onLinkPages={handleLinkPages}
          onCloseLinkingDialog={() => setShowLinkingDialog(false)}
          onDontShowLinkingAgain={() => {
            setShowLinkingDialog(false);
            setHasShownLinkingSuggestion(true);
          }}
          linkingProgress={linkingProgress}
          showImplementModal={false}
          selectedElement={null}
          customFunctionality=""
          isGeneratingIntent={false}
          onCloseImplementModal={() => { }}
          onSetCustomFunctionality={() => { }}
          onImplementChoice={() => { }}
          showDeleteConfirm={showDeleteConfirm}
          onConfirmDelete={confirmDelete}
          onCancelDelete={cancelDelete}
        />

        {/* Implementation Choice Modal */}
        <ImplementationModal
          showImplementModal={state.showImplementModal}
          selectedElement={state.selectedElement}
          customFunctionality={state.customFunctionality || ''}
          isGeneratingIntent={isGeneratingIntent}
          onCloseImplementModal={() => actions.setShowImplementModal(false)}
          onSetCustomFunctionality={actions.setCustomFunctionality}
          onImplementChoice={handleImplementChoice}
        />
      </div>

      {/* VERSIONING DISABLED: Debug Panel for Versioning */}
     
    </ErrorBoundary>
  );
}

export default EditorPageV3Refactored;